import re
import json
import time
from datetime import datetime
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup

class BBBProfileExtractor:
   def __init__(self):
       self.data = {}
       self.skip_domains = [
           'facebook', 'twitter', 'linkedin', 'instagram','youtube', 'bbb.org', 'bbbprograms.org', 
           'give.org', 'google.com', 'maps.google', 'googletagmanager', 'mouseflow.com', 
           'cloudflareinsights', 'e2ma.net', 'mailchimp.com', 'constantcontact.com', 
           'signup.', 'newsletter.' , 'livechatinc.com', 'subscribe.'
       ]

   def extract_profile(self, html_content: str) -> Dict[str, Any]:
       if not html_content or not html_content.strip():
           raise ValueError("HTML content cannot be empty")

       self._init_data()
       soup = BeautifulSoup(html_content, 'html.parser')
       
       self._extract_from_scripts(soup)
       self._extract_from_html(soup)
       self._post_process()
       
       return self.data

   def _init_data(self):
       self.data = {
           "timestamp": datetime.now().isoformat(),
           "business_name": None, "business_type": None, "accreditation": None,
           "phone": None, "email": None, "website_link": None,
           "address": {"street": None, "city": None, "state": None, "zip": None, "zipcode_extended": None},
           "rating": None, "reasons_for_rating": [],
           "business_details": {}, "management": {}, "business_categories": [], "additional_info": {}
       }

   def _extract_from_scripts(self, soup: BeautifulSoup):
       # Extract from webDigitalData
       script_tag = soup.find('script', string=re.compile(r'var webDigitalData\s*='))
       if script_tag:
           patterns = [
               r'var webDigitalData\s*=\s*({.*?});',
               r'var webDigitalData\s*=\s*({.*?})(?=</script>)',
               r'var webDigitalData\s*=\s*({.*?})(?=\s*</script>)',
               r'var webDigitalData\s*=\s*({.*})'
           ]
           
           for pattern in patterns:
               match = re.search(pattern, script_tag.string, re.DOTALL)
               if match:
                   try:
                       web_data = json.loads(match.group(1))
                       business_info = web_data.get('business_info', {})
                       page_info = web_data.get('page', {})
                       
                       self.data['business_name'] = business_info.get('business_name')
                       self.data['phone'] = business_info.get('business_phone')
                       
                       rating = business_info.get('business_rating')
                       self.data['rating'] = 'No Rating' if rating == 'NR' else rating
                       
                       accred = business_info.get('accredited_status')
                       self.data['accreditation'] = 'Accredited' if accred == 'AB' else 'Not Accredited'
                       
                       self.data['business_type'] = page_info.get('site_subsection3')
                       self.data['additional_info'].update({
                           'business_id': business_info.get('business_id'),
                           'business_zip': business_info.get('business_zip'),
                           'bbb_id': page_info.get('bbb_id'),
                           'has_logo': business_info.get('has_logo', 'N') == 'Y'
                       })
                       break
                   except json.JSONDecodeError:
                       continue

       # Extract from JSON-LD
       json_ld_scripts = soup.find_all('script', type='application/ld+json')
       for script in json_ld_scripts:
           if script.string:
               try:
                   ld_data = json.loads(script.string)
                   items = ld_data if isinstance(ld_data, list) else [ld_data]
                   
                   for item in items:
                       if item.get('@type') == 'LocalBusiness':
                           self._fill_if_empty('business_name', item.get('name'))
                           self._fill_if_empty('phone', item.get('telephone'))
                           if 'foundingDate' in item:
                               self.data['business_details']['Business Started'] = item['foundingDate']
                           
                           if not self.data['address']['street'] and 'address' in item:
                               addr = item['address']
                               self.data['address'].update({
                                   'street': addr.get('streetAddress'),
                                   'city': addr.get('addressLocality'),
                                   'state': addr.get('addressRegion')
                               })
                               postal_code = addr.get('postalCode')
                               if postal_code:
                                   if '-' in postal_code:
                                       zip_parts = postal_code.split('-', 1)
                                       self.data['address']['zip'] = zip_parts[0]
                                       self.data['address']['zipcode_extended'] = postal_code
                                   else:
                                       self.data['address']['zip'] = postal_code
                                       self.data['address']['zipcode_extended'] = postal_code
               except json.JSONDecodeError:
                   continue

   def _extract_from_html(self, soup: BeautifulSoup):
       # Basic info
       self._fill_if_empty('business_name', self._get_text(soup, ['h1[class*="business-name"]', 'h1']))
       self._fill_if_empty('phone', self._get_text(soup, ['a[href^="tel:"]']))
       self._fill_if_empty('rating', self._get_text(soup, ['span[class*="rating"]', 'dt.mr-2 + dd']))
       
       # Address
       if not self.data['address']['street']:
           address_text = self._get_text(soup, ['div[class*="address"] p', 'address', 'div[class*="location"]', '.address-line'], separator=' ')
           if address_text:
               self._parse_address(address_text)
       
       # Accreditation
       if not self.data.get('accreditation'):
           meta_desc = soup.find('meta', attrs={'name': 'description'})
           if meta_desc and meta_desc.get('content'):
               desc = meta_desc['content'].lower()
               if 'bbb accredited since' in desc:
                   date_match = re.search(r'bbb accredited since (\d{1,2}/\d{1,2}/\d{4})', desc)
                   self.data['accreditation'] = f"Accredited since {date_match.group(1)}" if date_match else 'Accredited'
               elif 'accredited' in desc and 'not' not in desc:
                   self.data['accreditation'] = 'Accredited'
       
       # Business details
       details_container = soup.find(lambda tag: tag.name in ['h2', 'h3'] and 'Business Details' in tag.text)
       if details_container:
           parent_section = details_container.find_parent()
           for dt in parent_section.find_all('dt'):
               key = dt.get_text(strip=True).replace(':', '')
               dd = dt.find_next_sibling('dd')
               if key and dd:
                   self.data['business_details'][key] = dd.get_text(separator='\n', strip=True)
       
       # Management
       management_text = (self.data['business_details'].get('Business Management') or 
                         self.data['business_details'].get('Principal Contacts') or 
                         self.data['business_details'].get('Customer Contacts'))
       if management_text:
           parts = [p.strip() for p in management_text.split(',')]
           self.data['management']['name'] = parts[0]
           if len(parts) > 1:
               self.data['management']['title'] = ', '.join(parts[1:])
       
       # Categories
       categories = set()
       cat_heading = soup.find(['h2', 'h3'], string=re.compile('Categories', re.I))
       if cat_heading:
           container = cat_heading.find_next(['div', 'ul'])
           if container:
               for link in container.find_all('a'):
                   categories.add(link.get_text(strip=True))
       
       if 'Business Categories' in self.data['business_details']:
           for cat in re.split(r'[,;|\n]', self.data['business_details']['Business Categories']):
               if cat.strip():
                   categories.add(cat.strip())
       
       self.data['business_categories'] = sorted(list(categories))
       
       # Website
       self._extract_website(soup)
       
       # Email
       self._extract_email(soup)
       
       # Rating reasons
       rating_heading = soup.find(['h2', 'h3', 'h4'], string=re.compile(r'BBB Rating', re.I))
       if rating_heading:
           rating_section = rating_heading.find_parent(['div', 'section'])
           if rating_section:
               reasons_element = rating_section.find(string=re.compile(r'Reasons for rating', re.I))
               if reasons_element:
                   reasons_container = reasons_element.find_parent()
                   if reasons_container:
                       next_element = reasons_container.find_next(['ul', 'ol', 'div', 'p'])
                       if next_element:
                           reasons_text = next_element.get_text(separator='\n', strip=True)
                           if reasons_text:
                               self.data['reasons_for_rating'] = [line.strip() for line in reasons_text.split('\n') if line.strip()]

   def _extract_website(self, soup: BeautifulSoup):
       # From business details
       website_text = (self.data['business_details'].get('Website') or 
                      self.data['business_details'].get('Web Site') or 
                      self.data['business_details'].get('Homepage'))
       
       if website_text:
           url_match = re.search(r'https?://[^\s]+', website_text)
           if url_match:
               url = url_match.group(0)
               if not any(domain in url.lower() for domain in ['bbb.org', 'bbbprograms.org', 'e2ma.net']):
                   self.data['website_link'] = url
                   return
           elif website_text.startswith('www.'):
               self.data['website_link'] = f"http://{website_text}"
               return
       
       # From HTML elements
       visit_links = soup.find_all('a', string=re.compile(r'Visit Website', re.I))
       for link in visit_links:
           href = link.get('href', '')
           if href.startswith('http') and not any(domain in href.lower() for domain in self.skip_domains):
               self.data['website_link'] = href
               return
       
       # Other external links
       for link in soup.find_all('a', href=True):
           href = link.get('href', '')
           text = link.get_text(strip=True).lower()
           
           if (any(domain in href.lower() for domain in self.skip_domains) or 
               any(word in text for word in ['share', 'facebook', 'twitter', 'social', 'bbb', 'signup', 'subscribe'])):
               continue
           
           if (href.startswith('http') and any(ext in href for ext in ['.com', '.org', '.net']) and
               not any(skip_word in href.lower() for skip_word in ['signup', 'subscribe', 'newsletter', 'email'])):
               self.data['website_link'] = href
               return

   def _extract_email(self, soup: BeautifulSoup):
       # From business details
       email_text = (self.data['business_details'].get('Email') or 
                    self.data['business_details'].get('E-mail') or 
                    self.data['business_details'].get('Contact Email'))
       
       if email_text:
           email_match = re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', email_text)
           if email_match:
               self.data['email'] = email_match.group(0)
               return
       
       # From mailto links
       for link in soup.find_all('a', href=re.compile(r'^mailto:')):
           email_href = link.get('href', '')
           if email_href.startswith('mailto:'):
               email = email_href[7:].split('?')[0].split('&')[0]
               if re.match(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', email):
                   self.data['email'] = email
                   return
       
       # From contact sections
       contact_sections = soup.find_all(['div', 'section'], class_=re.compile(r'contact|business-info', re.I))
       for section in contact_sections:
           section_text = section.get_text()
           email_matches = re.findall(r'\b[A-Za-z0-9][A-Za-z0-9._%+-]*@[A-Za-z0-9][A-Za-z0-9.-]+\.[A-Za-z]{2,}\b', section_text)
           for email in email_matches:
               if (len(email) > 5 and email.count('@') == 1 and '.' in email.split('@')[1] and
                   not any(skip in email.lower() for skip in ['example.com', 'test.com', 'bbb.org', 'texas.gov', 'tsbde'])):
                   self.data['email'] = email
                   return

   def _parse_address(self, address_text: str):
       address_text = re.sub(r'\s+', ' ', address_text.strip())
       
       zip_match = re.search(r'(\d{5})(?:-(\d{4}))?', address_text)
       if zip_match:
           self.data['address']['zip'] = zip_match.group(1)
           self.data['address']['zipcode_extended'] = f"{zip_match.group(1)}-{zip_match.group(2)}" if zip_match.group(2) else zip_match.group(1)
           address_text = address_text[:zip_match.start()].strip()
       
       state_match = re.search(r'\b([A-Z]{2})\b', address_text)
       if state_match:
           self.data['address']['state'] = state_match.group(1)
           address_text = address_text[:state_match.start()].strip()
       
       parts = [part.strip() for part in address_text.split(',')]
       if len(parts) >= 2:
           self.data['address']['street'] = parts[0]
           self.data['address']['city'] = parts[-1]
       elif len(parts) == 1:
           self.data['address']['street'] = parts[0]

   def _post_process(self):
       if self.data.get('business_name'):
           self.data['business_name'] = self.data['business_name'].split('|')[0].strip()
       
       if self.data.get('phone'):
           match = re.search(r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', self.data['phone'])
           if match:
               self.data['phone'] = match.group(0)
       
       if not self.data.get('business_type') and self.data['business_categories']:
           self.data['business_type'] = self.data['business_categories'][0]
       
       for field in ['street', 'city', 'state', 'zip', 'zipcode_extended']:
           if self.data['address'].get(field):
               self.data['address'][field] = str(self.data['address'][field]).strip()
       
       self.data['address'] = {k: v for k, v in self.data['address'].items() if v and str(v).strip()}
       
       if self.data.get('email'):
           self.data['email'] = self.data['email'].strip().lower()
       
       if self.data.get('website_link'):
           website = self.data['website_link'].strip()
           if not website.startswith(('http://', 'https://')):
               if website.startswith('www.') or '.' in website:
                   website = f"http://{website}"
           self.data['website_link'] = website
       
       self.data['business_details'] = {k: v for k, v in self.data['business_details'].items() if v and str(v).strip()}
       self.data['additional_info'] = {k: v for k, v in self.data['additional_info'].items() if v}

   def _fill_if_empty(self, key: str, value: Any):
       if value and not self.data.get(key):
           self.data[key] = value

   def _get_text(self, soup: BeautifulSoup, selectors: List[str], separator: str = '') -> Optional[str]:
       for selector in selectors:
           element = soup.select_one(selector)
           if element:
               return element.get_text(separator=separator, strip=True)
       return None


def extract_from_file(file_path: str, output_file: str = None) -> Dict[str, Any]:
   if not file_path:
       raise ValueError("File path cannot be empty")

   with open(file_path, 'r', encoding='utf-8') as f:
       html_content = f.read()

   extractor = BBBProfileExtractor()
   business_data = extractor.extract_profile(html_content)

   if output_file:
       with open(output_file, 'w', encoding='utf-8') as f:
           json.dump(business_data, f, indent=4, ensure_ascii=False)
       print(f"Data successfully extracted and saved to {output_file}")

   return business_data

def main():
   try:
       input_html_path = r"html-files/1.html"
       output_json_path = 'profile_data_optimized.json'
       
       extracted_data = extract_from_file(input_html_path, output_json_path)
       print("\n--- Extracted Data ---")
       print(json.dumps(extracted_data, indent=2, ensure_ascii=False))

   except FileNotFoundError:
       print(f"Error: Input file not found. Please check the path.")
   except Exception as e:
       print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
   main()
